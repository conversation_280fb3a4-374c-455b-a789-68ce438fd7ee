package com.creational;

//Abstract Product Interfaces
interface Button {
 void paint();
}

interface Checkbox {
 void paint();
}

//Concrete Products for Windows
class WindowsButton implements Button {
 public void paint() {
     System.out.println("Rendering a Windows Button");
 }
}

class WindowsCheckbox implements Checkbox {
 public void paint() {
     System.out.println("Rendering a Windows Checkbox");
 }
}

//Concrete Products for MacOS
class MacOSButton implements Button {
 public void paint() {
     System.out.println("Rendering a MacOS Button");
 }
}

class MacOSCheckbox implements Checkbox {
 public void paint() {
     System.out.println("Rendering a MacOS Checkbox");
 }
}

//Abstract Factory
interface GUIFactory {
	Button createButton(); // Returns an object of type But<PERSON> (interface)
    Checkbox createCheckbox();  // Returns an object of type Checkbox (interface)
}

//Concrete Factories
class WindowsFactory implements GUIFactory {
 public Button createButton() {
     return new WindowsButton();
 }

 public Checkbox createCheckbox() {
     return new WindowsCheckbox();
 }
}

class MacOSFactory implements GUIFactory {
 public Button createButton() {
     return new MacOSButton();
 }

 public Checkbox createCheckbox() {
     return new MacOSCheckbox();
 }
}

//Client Code
public class Abstract_factoryDP {
 public static void main(String[] args) {
     GUIFactory factory;

     // Configuring for Windows
     factory = new WindowsFactory();
     Button button1 = factory.createButton();
     Checkbox checkbox1 = factory.createCheckbox();
     button1.paint();
     checkbox1.paint();

     // Configuring for MacOS
     factory = new MacOSFactory();
     Button button2 = factory.createButton();
     Checkbox checkbox2 = factory.createCheckbox();
     button2.paint();
     checkbox2.paint();
 }
}




/*

The factory method returns an interface type (Button or Checkbox), 
so the client can use any concrete implementation of those interfaces (like WindowsButton, MacOSButton, etc.).


This ensures that the factory is decoupled from the specific implementations,
 allowing flexibility to add new types (like LinuxButton) without modifying the client code.

*/