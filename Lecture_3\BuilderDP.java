package com.creational;

//Product Class
class Car {
 private String engine;
 private int wheels;
 private boolean sunroof;

 public void setEngine(String engine) { 
	 this.engine = engine;
	 }
 public void setWheels(int wheels) { 
	 this.wheels = wheels; 
	 }
 public void setSunroof(boolean sunroof) { 
	 this.sunroof = sunroof;
	 }

 public String toString() {
     return "Car [Engine=" + engine + ", Wheels=" + wheels + ", Sunroof=" + sunroof + "]";
 }
}

//Builder Interface
interface CarBuilder {
 void buildEngine();
 void buildWheels();
 void buildSunroof();
 Car getCar();
}

//Concrete Builder
class SportsCarBuilder implements CarBuilder {
 private Car car = new Car();

 public void buildEngine() { 
	 car.setEngine("V8 Engine"); 
	 }
 public void buildWheels() {
	 car.setWheels(4); 
	 }
 public void buildSunroof() {
	 car.setSunroof(true);
	 }
 public Car getCar() { 
	 return car;
	 }
}

//Director Class
class Director {
 public Car constructCar(CarBuilder builder) {
     builder.buildEngine();
     builder.buildWheels();
     builder.buildSunroof();
     return builder.getCar();
 }
}

//Test Builder Pattern
public class BuilderDP {
 public static void main(String[] args) {
     CarBuilder builder = new SportsCarBuilder();
     Director director = new Director();
     
     Car sportsCar = director.constructCar(builder);
     System.out.println(sportsCar);
 }
}




/*


director.constructCar(builder): The Director calls the builder to construct the car. During this process, the builder sets the engine, wheels, and sunroof for the Car object.

getCar() is called: The builder returns the constructed Car object by calling getCar().

System.out.println(sportsCar): Now, the sportsCar object (which is the Car object returned by getCar()) is passed to System.out.println().

toString() is triggered: When you try to print the Car object, Java automatically calls the toString() method of the Car class to get a string representation of the object. 
This is because System.out.println() expects a string, and objects are automatically converted to strings by invoking their toString() method.

*/