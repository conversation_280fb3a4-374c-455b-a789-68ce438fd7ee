package com.Structural;

//Subsystems
class CPU {
 public void start() {
     System.out.println("CPU started");
 }
}

class Memory {
 public void load() {
     System.out.println("Memory loaded");
 }
}

class HardDrive {
 public void read() {
     System.out.println("HardDrive reading");
 }
}

//Facade
class Computer {
 private CPU cpu = new CPU();
 private Memory memory = new Memory();
 private HardDrive hardDrive = new HardDrive();

 public void start() {
     cpu.start();
     memory.load();
     hardDrive.read();
 }
}

//Test Facade Pattern
public class FacadeDP {
 public static void main(String[] args) {
     Computer computer = new Computer();
     computer.start();
     // Output:
     // CPU started
     // Memory loaded
     // HardDrive reading
 }
}