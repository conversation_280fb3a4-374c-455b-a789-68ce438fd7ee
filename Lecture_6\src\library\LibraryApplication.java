/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package library;

/**
 *
 * <AUTHOR>
 */
import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.io.*;
import java.net.*;
import java.util.HashMap;

public class LibraryApplication extends JFrame {

    private JRadioButton studentBtn, lecturerBtn, publicBtn;
    private JToggleButton lendReadToggle;
    private JComboBox<String> bookCategoryCombo;
    private JCheckBox phoneCheck, umbrellaCheck, bagsCheck, otherCheck;
    private JPasswordField readerIdField;
    private JSlider paymentSlider;
    private JButton nextButton;
    private JTextArea inputDisplay;
    private JPanel mainPanel;
    private JTextField sliderValueField;

    private final HashMap<String, String> readers = new HashMap<>();
    private JFrame secondFrame;

    public LibraryApplication() {
        
        setTitle("Library Application");
        setSize(800, 600);  
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        mainPanel = new JPanel(new BorderLayout(20, 20));
        add(mainPanel);

       
        loadReaderData();

        
        JPanel leftPanel = new JPanel(new GridLayout(10, 1, 10, 10));
        mainPanel.add(leftPanel, BorderLayout.WEST);

        
        studentBtn = new JRadioButton("Student");
        lecturerBtn = new JRadioButton("Lecturer");
        publicBtn = new JRadioButton("Public");
        ButtonGroup readerGroup = new ButtonGroup();
        readerGroup.add(studentBtn);
        readerGroup.add(lecturerBtn);
        leftPanel.add(createPanel("Reader Type:", studentBtn, lecturerBtn, publicBtn));

        
        lendReadToggle = new JToggleButton("Lend");
        lendReadToggle.addActionListener(e -> lendReadToggle.setText(lendReadToggle.isSelected() ? "Read" : "Lend"));
        leftPanel.add(createPanel("Activity:", lendReadToggle));

        
        bookCategoryCombo = new JComboBox<>(new String[]{"Literature", "Science", "Technology", "History"});
        leftPanel.add(createPanel("Book Category:", bookCategoryCombo));

      
        phoneCheck = new JCheckBox("Phone");
        umbrellaCheck = new JCheckBox("Umbrella");
        bagsCheck = new JCheckBox("Bags");
        otherCheck = new JCheckBox("Other");
        leftPanel.add(createPanel("Belongings:", phoneCheck, umbrellaCheck, bagsCheck, otherCheck));

        
        readerIdField = new JPasswordField(10);
        leftPanel.add(createPanel("Reader ID:", readerIdField));

       
        paymentSlider = new JSlider(JSlider.HORIZONTAL, 100, 3200, 100);
        paymentSlider.setMajorTickSpacing(500);
        paymentSlider.setPaintTicks(true);
        paymentSlider.setPaintLabels(true);
        paymentSlider.addChangeListener(e -> {
            int value = paymentSlider.getValue();
            sliderValueField.setText(value + " Rs");  
        });

        JPanel sliderPanel = new JPanel();
        sliderPanel.setLayout(new BorderLayout());
        sliderPanel.add(paymentSlider, BorderLayout.CENTER);

        
        sliderValueField = new JTextField(5);
        sliderValueField.setEditable(false);
        sliderPanel.add(sliderValueField, BorderLayout.EAST);

        leftPanel.add(createPanel("Payment Slider:", sliderPanel));

        
        nextButton = new JButton("Next");
        leftPanel.add(createPanel("", nextButton));

        
        inputDisplay = new JTextArea(50, 50);
        inputDisplay.setEditable(false);
        JScrollPane scrollPane = new JScrollPane(inputDisplay);
        leftPanel.add(scrollPane);

        
        JPanel rightPanel = new JPanel();
        mainPanel.add(rightPanel, BorderLayout.EAST);

        try {
           
            URL imageUrl = new URL("https://png.pngtree.com/thumb_back/fw800/background/20240914/pngtree-student-reading-book-in-library-with-backpack-stock-photo-royalty-free-image_16204892.jpg");
            ImageIcon icon = new ImageIcon(imageUrl);
            JLabel pictureLabel = new JLabel(icon);
            rightPanel.add(pictureLabel);
        } catch (MalformedURLException e) {
            JOptionPane.showMessageDialog(this, "Invalid image URL.", "Error", JOptionPane.ERROR_MESSAGE);
        }

        
        nextButton.addActionListener(new NextButtonListener());
    }

    private JPanel createPanel(String label, JComponent... components) {
        JPanel panel = new JPanel();
        panel.setLayout(new FlowLayout(FlowLayout.LEFT));
        if (!label.isEmpty()) {
            panel.add(new JLabel(label));
        }
        for (JComponent comp : components) {
            panel.add(comp);
        }
        return panel;
    }

    private void loadReaderData() {
        try (BufferedReader br = new BufferedReader(new FileReader("readers.txt"))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length == 2) {
                    readers.put(parts[0].trim(), parts[1].trim());
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "Error loading reader data.", "Error", JOptionPane.ERROR_MESSAGE);
        }
    }

    private class NextButtonListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            // Validate Inputs
            String readerType = studentBtn.isSelected() ? "Student" :
                                lecturerBtn.isSelected() ? "Lecturer" : "Public";
            String activity = lendReadToggle.isSelected() ? "Read" : "Lend";
            String bookCategory = (String) bookCategoryCombo.getSelectedItem();
            StringBuilder belongings = new StringBuilder();
            if (phoneCheck.isSelected()) belongings.append("Phone, ");
            if (umbrellaCheck.isSelected()) belongings.append("Umbrella, ");
            if (bagsCheck.isSelected()) belongings.append("Bags, ");
            if (otherCheck.isSelected()) belongings.append("Other");
            String readerId = new String(readerIdField.getPassword());

            if (!readers.containsKey(readerId)) {
                JOptionPane.showMessageDialog(LibraryApplication.this, "Invalid Reader ID.", "Error", JOptionPane.ERROR_MESSAGE);
                return;
            }

            String readerName = readers.get(readerId);
            int paymentPerHour = readerType.equals("Student") ? 100 : readerType.equals("Lecturer") ? 500 : 800;
            int totalPayment = paymentSlider.getValue();

            
            if (activity.equals("Lend") && totalPayment < 1000) {
                JOptionPane.showMessageDialog(LibraryApplication.this, 
                        "Minimum amount for lending is 1000 Rs. Please select more than 1000 Rs.", 
                        "Error", JOptionPane.ERROR_MESSAGE);
                return; 
            }

            int hours = totalPayment / paymentPerHour;

            inputDisplay.setText(String.format(
                "Reader Name: %s\n" +
                "Reader ID: %s\n" +
                "Reader Type: %s\n" +
                "Activity: %s\n" +
                "Book Category: %s\n" +
                "Belongings: %s\n" +
                "Hours: %d\n" +
                "Total Payment: %d\n",
                readerName, readerId, readerType, activity, bookCategory, belongings, hours, totalPayment
            ));

            
            showSecondFrame(readerName, readerId, activity);
        }
    }

    private void showSecondFrame(String readerName, String readerId, String activity) {
        secondFrame = new JFrame("Library Ticket");
        secondFrame.setSize(400, 400);
        secondFrame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        JPanel panel = new JPanel();
        panel.setBackground(Color.LIGHT_GRAY);
        panel.setLayout(new BorderLayout());

        JTextArea ticketArea = new JTextArea();
        ticketArea.setBackground(new Color(173, 216, 230)); 
        ticketArea.setForeground(Color.BLACK);
        ticketArea.setEditable(false);

        if (activity.equals("Read")) {
            ticketArea.setText(String.format("Dear %s (ID: %s),\n\nYou are allowed to read for %d hours.\nEnjoy your session!\n",
                    readerName, readerId, paymentSlider.getValue() / 100));  
        } else {
            ticketArea.setText(String.format("Dear %s (ID: %s),\n\nPlease get assistance from the library management team for lending.\n",
                    readerName, readerId));
        }

        JScrollPane scrollPane = new JScrollPane(ticketArea);
        panel.add(scrollPane, BorderLayout.CENTER);

        JButton printButton = new JButton("Print Ticket");
        printButton.addActionListener(e -> JOptionPane.showMessageDialog(secondFrame, "Ticket Printed Successfully!"));
        panel.add(printButton, BorderLayout.SOUTH);

        secondFrame.add(panel);
        secondFrame.setVisible(true);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            LibraryApplication app = new LibraryApplication();
            app.setVisible(true);
        });
    }
}

