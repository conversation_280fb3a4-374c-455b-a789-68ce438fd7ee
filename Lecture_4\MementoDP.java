package com.Behavioural;

//Memento
class Memento {
 private String state;

 public Memento(String state) {
     this.state = state;
 }

 public String getState() {
     return state;
 }
}

//Originator
class Originator {
 private String state;

 public void setState(String state) {
     this.state = state;
 }

 public String getState() {
     return state;
 }

 public Memento saveStateToMemento() {
     return new Memento(state);
 }

 public void restoreStateFromMemento(Memento memento) {
     state = memento.getState();
 }
}

//Caretaker
class Caretaker {
 private java.util.List<Memento> mementoList = new java.util.ArrayList<>();

 public void add(Memento state) {
     mementoList.add(state);
 }

 public Memento get(int index) {
     return mementoList.get(index);
 }
}

//Test Memento Pattern
public class MementoDP {
 public static void main(String[] args) {
     Originator originator = new Originator();
     Caretaker caretaker = new Caretaker();

     originator.setState("State1");
     caretaker.add(originator.saveStateToMemento());

     originator.setState("State2");
     caretaker.add(originator.saveStateToMemento());

     originator.restoreStateFromMemento(caretaker.get(0));
     System.out.println("Restored State: " + originator.getState()); // Output: Restored State: State1
 }
}
