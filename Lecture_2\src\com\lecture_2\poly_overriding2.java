package com.lecture_2;


	// Java program to demonstrate application
	// of overriding in Java

	// Base Class
	class Employee {
	    public static int base = 10000;
	    //base: A static integer variable, representing the base salary common to all employees.
	   // Static means it's shared across all instances of the class.
	    int salary() { 
	    	return base;
	    	}
	}
	

	// Inherited class
	class Manager extends Employee {
	    // This method overrides salary() of Parent
	    int salary() {
	    	return base + 20000; 
	    	}
	}

	// Inherited class
	class Clerk extends Employee {
	    // This method overrides salary() of Parent
	    int salary() {
	    	return base + 10000;
	    	}
	}

	// Driver class
	public class poly_overriding2 {
	    // This method can be used to print the salary of
	    // any type of employee using base class reference
	    static void printSalary(Employee e)
	    {
	    	/*
	    	 Accepts a reference of type Employee (base class).
	 Invokes the salary() method using this reference.
Demonstrates polymorphism: The actual method called is determined at runtime based on the object type.
	    	 */
	        System.out.println(e.salary());
	    }

	    public static void main(String[] args)
	    {
	        Employee obj1 = new Manager();

	        // We could also get type of employee using
	        // one more overridden method.loke getType()
	        System.out.print("Manager's salary : ");
	        printSalary(obj1);

	        Employee obj2 = new Clerk();
	        System.out.print("Clerk's salary : ");
	        printSalary(obj2);
	    }
	}
