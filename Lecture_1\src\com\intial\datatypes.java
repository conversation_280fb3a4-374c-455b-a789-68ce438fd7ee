package com.intial;

public class datatypes {
	public static void main(String[] args) {
        // Integer type
        int myInt = 100;
        System.out.println("Integer: " + myInt);

        // Float type
        float myFloat = 5.75f;
        System.out.println("Float: " + myFloat);

        // Double type
        double myDouble = 19.99;
        System.out.println("Double: " + myDouble);

        // Character type
        char myChar = 'A';
        System.out.println("Character: " + myChar);

        // Boolean type
        boolean myBool = true;
        System.out.println("Boolean: " + myBool);

        // Byte type
        byte myByte = 127;
        System.out.println("Byte: " + myByte);

        // Short type
        short myShort = 32000;
        System.out.println("Short: " + myShort);

        // Long type
        long myLong = 9223372036854775807L;
        System.out.println("Long: " + myLong);
    }
}
