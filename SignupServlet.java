/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/JSP_Servlet/Servlet.java to edit this template
 */

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@WebServlet("/SignupServlet")
public class SignupServlet extends HttpServlet {

    private static final String DB_URL = "***************************************";
    private static final String DB_USER = "root"; 
    private static final String DB_PASSWORD = ""; 

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        // Retrieve form data
        String name = request.getParameter("name");
        String age = request.getParameter("age");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");  
        String bloodGroup = request.getParameter("blood_group");
        String address = request.getParameter("address");
        String username = request.getParameter("username");
        String password = request.getParameter("password");

      
        if (name == null || age == null || phone == null || email == null || bloodGroup == null || 
            address == null || username == null || password == null || name.isEmpty() || 
            age.isEmpty() || phone.isEmpty() || email.isEmpty() || bloodGroup.isEmpty() || 
            address.isEmpty() || username.isEmpty() || password.isEmpty()) {
            
            response.sendRedirect("signup.jsp?error=All fields are required!");
            return;
        }

        try {
            
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            
            // SQL query to insert user data including email
            String sql = "INSERT INTO patients (name, age, phone, email, blood_group, address, username, password) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, name);
            stmt.setInt(2, Integer.parseInt(age));  
            stmt.setString(3, phone);
            stmt.setString(4, email); 
            stmt.setString(5, bloodGroup);
            stmt.setString(6, address);
            stmt.setString(7, username);
            stmt.setString(8, password);

            int rowsInserted = stmt.executeUpdate();
            stmt.close();
            conn.close();

            
            if (rowsInserted > 0) {
                response.sendRedirect("patient.jsp?message=Signup successful! You can now log in.");
            } else {
                response.sendRedirect("signup.jsp?error=Signup failed! Please try again.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect("signup.jsp?error=Database error! Please try again.");
        }
    }
}
