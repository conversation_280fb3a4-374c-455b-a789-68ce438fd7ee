package com.intial;

public class loopsexample {
public static void main(String[] args) {
        
        // **For Loop**: Used when you know the number of iterations beforehand.
        System.out.println("For Loop:");
        for (int i = 1; i <= 5; i++) {
            System.out.println(i); // Print the value of i
        }

        // **While Loop**: Used when the number of iterations is not known.
        System.out.println("\nWhile Loop:");
        int j = 1;
        while (j <= 5) {
            System.out.println(j); // Print the value of j
            j++; // Increment the value of j
        }

        // **Do-While Loop**: Executes the block of code at least once, then checks the condition.
        System.out.println("\nDo-While Loop:");
        int k = 1;
        do {
            System.out.println(k); // Print the value of k
            k++; // Increment the value of k
        } while (k <= 5); // The condition is checked after the block is executed

    }
	
	
}
