package com.creational;

//Prototype Interface
interface Prototype {
 Prototype clone();
}

//Concrete Prototype
class Employee implements Prototype {
 private String name;
 private String position;

 public Employee(String name, String position) {
     this.name = name;
     this.position = position;
 }

 public void setName(String name) {
     this.name = name;
 }

 @Override
 public Prototype clone() {
     return new Employee(this.name, this.position);
 }

 @Override
 public String toString() {
     return "Employee [Name: " + name + ", Position: " + position + "]";
 }
}

//Test Prototype Pattern
public class PrototypeDP {
 public static void main(String[] args) {
     Employee originalEmployee = new Employee("<PERSON>", "Manager");
     System.out.println("Original: " + originalEmployee);

     // Clone the employee
     Employee clonedEmployee = (Employee) originalEmployee.clone();
     clonedEmployee.setName("<PERSON> Smith");

     System.out.println("Cloned: " + clonedEmployee);
 }
}


/*

(Employee) is a type cast.
Since the clone() method returns a Prototype object, and originalEmployee.
clone() implements this method, 
its return type is Prototype.


The cast (Employee) tells the Java compiler:
"I know the actual object type returned by clone() is Employee, so treat it as an Employee."
Without this cast, the compiler would throw an error because it cannot automatically determine the specific type.
Example Without Casting:


You will get a compiler error because the clone() method's return type is Prototype, 
and the compiler doesn't know that the object is of type Employee.
*/