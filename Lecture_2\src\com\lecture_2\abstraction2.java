package com.lecture_2;

/*
	abstract class Job {
	    String jobTitle;

	    // Constructor
	    public Job(String jobTitle) {
	        this.jobTitle = jobTitle;
	    }

	    // Abstract method (no implementation)
	    abstract void performDuties();

	    // Concrete method (has implementation)
	    void displayJobTitle() {
	        System.out.println("Job Title: " + jobTitle);
	    }
	}

	// Subclass 1: Developer
	class Developer extends Job {
	    public Developer() {
	        super("Developer");
	    }

	    @Override
	    void performDuties() {
	        System.out.println("Writing, testing, and maintaining code.");
	    }
	}

	// Subclass 2: Doctor
	class Doctor extends Job {
	    public Doctor() {
	        super("Doctor");
	    }

	    @Override
	    void performDuties() {
	        System.out.println("Diagnosing and treating patients.");
	    }
	}

	// Subclass 3: Teacher
	class Teacher extends Job {
	    public Teacher() {
	        super("Teacher");
	    }

	    @Override
	    void performDuties() {
	        System.out.println("Teaching and guiding students.");
	    }
	}

	// Driver class
	public class abstraction2 { 
	    public static void main(String[] args) {
	        // Polymorphism
	        Job job1 = new Developer();
	        Job job2 = new Doctor();
	        Job job3 = new Teacher();

	        // Display details for each job
	        job1.displayJobTitle();
	        job1.performDuties();

	        job2.displayJobTitle();
	        job2.performDuties();

	        job3.displayJobTitle();
	        job3.performDuties();
	    }
	}
*/	
	
	
/*
 
 The super keyword is already used in the constructor of the subclasses (Developer, Doctor, Teacher) to call the parent class (Job) constructor. 
  This ensures that the jobTitle attribute of the Job class is initialized correctly
  
 */

	

// Abstract class
abstract class Job {
    String jobTitle;

    // Constructor
    public Job(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    // Abstract method (no implementation)
    abstract void performDuties();

    // Concrete method (has implementation)
    void displayJobTitle() {
        System.out.println("Job Title: " + jobTitle);
    }

    // General method for describing a job
    void describeJob() {
        System.out.println("This is a general description of a job.");
    }
}

// Subclass 1: Developer
class Developer extends Job {
    public Developer() {
        super("Developer"); // Calls the parent class constructor
    }

    @Override
    void performDuties() {
        System.out.println("Writing, testing, and maintaining code.");
    }

    @Override
    void describeJob() {
        super.describeJob(); // Calls the parent class method
        System.out.println("As a Developer, you work with software systems.");
    }
}

// Subclass 2: Doctor
class Doctor extends Job {
    public Doctor() {
        super("Doctor"); // Calls the parent class constructor
    }

    @Override
    void performDuties() {
        System.out.println("Diagnosing and treating patients.");
    }

    @Override
    void describeJob() {
        super.describeJob(); // Calls the parent class method
        System.out.println("As a Doctor, you take care of people's health.");
    }
}

// Subclass 3: Teacher
class Teacher extends Job {
    public Teacher() {
        super("Teacher"); // Calls the parent class constructor
    }

    @Override
    void performDuties() {
        System.out.println("Teaching and guiding students.");
    }

    @Override
    void describeJob() {
        super.describeJob(); // Calls the parent class method
        System.out.println("As a Teacher, you educate and inspire students.");
    }
}

// Driver class
public class abstraction2 {
    public static void main(String[] args) {
        // Polymorphism
        Job job1 = new Developer();
        Job job2 = new Doctor();
        Job job3 = new Teacher();

        // Display details for each job
        job1.displayJobTitle();
        job1.performDuties();
        job1.describeJob();

        job2.displayJobTitle();
        job2.performDuties();
        job2.describeJob();

        job3.displayJobTitle();
        job3.performDuties();
        job3.describeJob();
    }
}	
	