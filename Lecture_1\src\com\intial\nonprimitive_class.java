package com.intial;

public class nonprimitive_class {
	String name;
    int age;

    public void displayInfo() {
        System.out.println("Name: " + name + ", Age: " + age);
    }

    public static void main(String[] args) {
    	nonprimitive_class person = new nonprimitive_class();
        person.name = "<PERSON>";
        person.age = 25;
        person.displayInfo();
    }
}









/*

Object Creation: new Person() creates an object of the Person class in memory.
Reference Assignment: The memory address of the new object is stored in the reference variable person.
Usage: You can now use the person reference to access the object’s fields (name, age) and methods (displayInfo()).
*/