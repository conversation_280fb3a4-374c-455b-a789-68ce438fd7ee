package com.intial;

public class enumvariable {
	    enum Day { MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY }

	    public static void main(String[] args) {
	        Day today = Day.WEDNESDAY;
	        System.out.println("Today is: " + today);
	    }
	}
/*
 
 enum Day:
This defines an enumeration called Day.
The enum keyword is used to define a fixed set of constants. In this case, it represents the days of the week: MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, and SUNDAY.
These constants are implicitly assigned ordinal values starting from 0. So, MONDAY has a value of 0, TUESDAY is 1, and so on, until SUNDAY, which has a value of 6.
This line creates a variable today of type Day.
 */
