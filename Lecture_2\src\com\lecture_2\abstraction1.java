package com.lecture_2;


	abstract class Course {
	    String courseName;
	    String instructor;

	    // Abstract methods
	    abstract double calculateFee();
	    public abstract String toString();

	    // Abstract class can have a constructor
	    public Course(String courseName, String instructor) {
	        System.out.println("Course constructor called");
	        this.courseName = courseName;
	        this.instructor = instructor;
	    }

	    // Concrete method
	    public String getInstructor() {
	        return instructor;
	    }

	    public String getCourseName() {
	        return courseName;
	    }
	}

	class OnlineCourse extends Course {
	    double baseFee;
	    double platformFee;

	    public OnlineCourse(String courseName, String instructor, double baseFee, double platformFee) {
	        // Calling Course constructor
	        super(courseName, instructor);
	        System.out.println("OnlineCourse constructor called");
	        this.baseFee = baseFee;
	        this.platformFee = platformFee;
	    }

	    @Override
	    double calculateFee() {
	        return baseFee + platformFee;
	    }

	    @Override
	    public String toString() {
	        return "Online Course: " + getCourseName() +
	                ", Instructor: " + getInstructor() +
	                ", Total Fee: $" + calculateFee();
	    }
	}

	class OfflineCourse extends Course {
	    double baseFee;
	    double labFee;

	    public OfflineCourse(String courseName, String instructor, double baseFee, double labFee) {
	        // Calling Course constructor
	        super(courseName, instructor);
	        System.out.println("OfflineCourse constructor called");
	        this.baseFee = baseFee;
	        this.labFee = labFee;
	    }

	    @Override
	    double calculateFee() {
	        return baseFee + labFee;
	    }

	    @Override
	    public String toString() {
	        return "Offline Course: " + getCourseName() +
	                ", Instructor: " + getInstructor() +
	                ", Total Fee: $" + calculateFee();
	    }
	}

	public class abstraction1 {
	    public static void main(String[] args) {
	        // Creating objects using the abstract class reference
	        Course c1 = new OnlineCourse("Java Programming", "Mr. Smith", 100, 20);
	        Course c2 = new OfflineCourse("Data Structures", "Dr. Lee", 150, 50);

	        // Printing details of each course
	        System.out.println(c1.toString());
	        System.out.println(c2.toString());
	    }
	}
