package com.intial;

public class Constructor {
	  int number;

	    // Default constructor
	    public Constructor() {
	        number = 10; // Initialize field
	    }

	    public static void main(String[] args) {
	        Constructor obj = new Constructor();
	        System.out.println("Number: " + obj.number); // Output: Number: 10
	    }
}




/*Key Characteristics of a Constructor
Same Name as the Class:
1. A constructor's name must match the class name.
2. No Return Type:
3. Unlike methods, constructors do not have a return type (not even void).
4. Automatically Called:
5. A constructor is called automatically when an object is instantiated using the new keyword.
*/