package com.lecture_2;


	// Defining the interface
	interface ProgrammingLanguage {
	    // Abstract methods (no implementation)
	    void compile();
	    void execute();
	    String getLanguageName();
	}

	// Implementing the interface: Java
	class JavaLanguage implements ProgrammingLanguage {
	    @Override
	    public void compile() {
	        System.out.println("Java code is compiled into bytecode by the Java Compiler.");
	    }

	    @Override
	    public void execute() {
	        System.out.println("Java bytecode is executed by the Java Virtual Machine (JVM).");
	    }

	    @Override
	    public String getLanguageName() {
	        return "Java";
	    }
	}

	// Implementing the interface: Python
	class PythonLanguage implements ProgrammingLanguage {
	    @Override
	    public void compile() {
	        System.out.println("Python is an interpreted language and does not require compilation.");
	    }

	    @Override
	    public void execute() {
	        System.out.println("Python code is executed directly by the Python interpreter.");
	    }

	    @Override
	    public String getLanguageName() {
	        return "Python";
	    }
	}

	// Implementing the interface: C++
	class CppLanguage implements ProgrammingLanguage {
	    @Override
	    public void compile() {
	        System.out.println("C++ code is compiled into machine code by a compiler like GCC or Clang.");
	    }

	    @Override
	    public void execute() {
	        System.out.println("C++ compiled machine code is executed directly by the operating system.");
	    }

	    @Override
	    public String getLanguageName() {
	        return "C++";
	    }
	}

	// Driver class
	public class abstraction3 {
	    public static void main(String[] args) {
	        // Using the interface reference to hold objects of implementing classes
	        ProgrammingLanguage java = new JavaLanguage();
	        ProgrammingLanguage python = new PythonLanguage();
	        ProgrammingLanguage cpp = new CppLanguage();

	        // Displaying details for each programming language
	        System.out.println("Programming Language: " + java.getLanguageName());
	        java.compile();
	        java.execute();

	        System.out.println("\nProgramming Language: " + python.getLanguageName());
	        python.compile();
	        python.execute();

	        System.out.println("\nProgramming Language: " + cpp.getLanguageName());
	        cpp.compile();
	        cpp.execute();
	    }
	}

