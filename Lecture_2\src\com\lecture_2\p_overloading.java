package com.lecture_2;

/*
public class p_overloading {

    // Method to add two integers
    public int add(int a, int b) {
        return a + b;
    }

    // Method to add three integers
    public int add(int a, int b, int c) {
        return a + b + c;
    }

    // Method to add two doubles
    public double add(double a, double b) {
        return a + b;
    }

    // Method to concatenate two strings
    public String add(String a, String b) {
        return a + b;
    }

    public static void main(String[] args) {
    	p_overloading calculator = new p_overloading();

        // Calling the overloaded methods
        System.out.println("Sum of 5 and 10 is: " + calculator.add(5, 10));
        System.out.println("Sum of 5, 10, and 15 is: " + calculator.add(5, 10, 15));
        System.out.println("Sum of 3.5 and 2.7 is: " + calculator.add(3.5, 2.7));
        System.out.println("Concatenation of 'Hello' and 'World' is: " + calculator.add("Hello", "World"));
    }
    
}


*/


 class p_overloading1{
   // Method to add two integers
    public int add(int a, int b) {
        return a + b;
    }

    // Method to add three integers
    public int add(int a, int b, int c) {
        return a + b + c;
    }

    // Method to add two doubles
    public double add(double a, double b) {
        return a + b;
    }

    // Method to concatenate two strings
    public String add(String a, String b) {
        return a + b;
    }
 }
 public class p_overloading {

  

    public static void main(String[] args) {
    	p_overloading1 calculator = new p_overloading1();

        // Calling the overloaded methods
        System.out.println("Sum of 5 and 10 is: " + calculator.add(5, 10));
        System.out.println("Sum of 5, 10, and 15 is: " + calculator.add(5, 10, 15));
        System.out.println("Sum of 3.5 and 2.7 is: " + calculator.add(3.5, 2.7));
        System.out.println("Concatenation of 'Hello' and 'World' is: " + calculator.add("Hello", "World"));
    }
    
}
 

