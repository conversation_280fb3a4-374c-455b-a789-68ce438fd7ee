package com.lecture_2;


	//Hybrid Inheritance
	class C {
	public void Print3() {
	System.out.println("C is the Parent Class to all A,B,inheritance2");
	}
	}
	class A extends B {
	public void Print2() {
	System.out.println("A inherits from B, which indirectly inherits from C (multilevel inheritance).");
	}
	}
	class B extends C {
	public void Print1() {
	System.out.println("B directly inherits from C (single inheritance)");
	}
	}
	public class inheritance2 extends A {
	public void Print() {
	System.out.println("inheritance2 inherits from A, making it a part of a multilevel inheritance chain rooted at C.");
	}
	public static void main(String args[]) {
	//A w = new A();
	//B x = new B();
	//C y = new C();
	inheritance2 z = new inheritance2();
	//y.Print();
	//w.Print();
	//x.Print();
	z.Print();
	z.Print1();
	z.Print3();
	z.Print2();
	}
	}

