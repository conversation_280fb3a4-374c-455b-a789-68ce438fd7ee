package com.lecture_2;

//Base class
class Animals {
 void makeSound() {
     System.out.println("Animal makes a sound");
 }
}

//Subclass
class Dogs extends Animals {
 // Method overriding
 void makeSound() {
     System.out.println("Dog barks");
 }
}

//Another subclass
class Cats extends Animals {
 // Method overriding
 void makeSound() {
     System.out.println("Cat meows");
 }
}

public class poly_overriding1 {
 public static void main(String[] args) {
     Animals animal1 = new Dogs();
     Animals animal2 = new Cats();

     // Method calls
     animal1.makeSound(); // Output: Dog barks
     animal2.makeSound(); // Output: Cat meows
 }
}



/*

Method Overriding
Overriding occurs when a subclass provides a specific implementation of a method that is already defined in its superclass.
The method in the subclass has the same name, return type, and parameters as the method in the superclass

*/



/*

animal1 is declared as type Animals but assigned an instance of Dogs.
At runtime, Java determines the actual object type (Dogs) and calls the overridden makeSound method of the Dogs class.
The output is: Dog barks.


animal2 is declared as type Animals but assigned an instance of Cats.
Similarly, at runtime, Java determines the actual object type (Cats) and calls the overridden makeSound method of the Cats class.
The output is: Cat meows.

*/