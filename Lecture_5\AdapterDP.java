package com.Structural;

//Target Interface
interface DocumentProcessor {
 void processDocument(String fileName);
}

//Adaptee 1
class PDFReader {
 void readPDF(String fileName) {
     System.out.println("Processing PDF Document: " + fileName);
 }
}

//Adaptee 2
class WordReader {
 void readWord(String fileName) {
     System.out.println("Processing Word Document: " + fileName);
 }
}

//Adapter for PDFReader
class PDFAdapter implements DocumentProcessor {
 private PDFReader pdfReader;

 public PDFAdapter() {
     this.pdfReader = new PDFReader();
 }

 public void processDocument(String fileName) {
     pdfReader.readPDF(fileName);
 }
}

//Adapter for WordReader
class WordAdapter implements DocumentProcessor {
 private WordReader wordReader;

 public WordAdapter() {
     this.wordReader = new WordReader();
 }

 public void processDocument(String fileName) {
     wordReader.readWord(fileName);
 }
}

//Client
public class AdapterDP {
 public static void main(String[] args) {
     // Using adapters to handle document processing
     DocumentProcessor pdfProcessor = new PDFAdapter();
     DocumentProcessor wordProcessor = new WordAdapter();

     pdfProcessor.processDocument("report.pdf"); // Output: Processing PDF Document: report.pdf
     wordProcessor.processDocument("resume.docx"); // Output: Processing Word Document: resume.docx
 }
}
