package com.Structural;

//Component Interface
interface Component {
 void showDetails();
}

//Leaf
class Developer implements Component {
 private String name;

 public Developer(String name) {
     this.name = name;
 }

 public void showDetails() {
     System.out.println("Developer: " + name);
 }
}

//Composite
class Team implements Component {
 private java.util.List<Component> members = new java.util.ArrayList<>();

 public void addMember(Component member) {
     members.add(member);
 }

 public void showDetails() {
     for (Component member : members) {
         member.showDetails();
     }
 }
}

//Test Composite Pattern
public class CompositeDP {
 public static void main(String[] args) {
     Developer dev1 = new Developer("Alice");
     Developer dev2 = new Developer("Bob");

     Team team = new Team();
     team.addMember(dev1);
     team.addMember(dev2);

     team.showDetails();
     // Output:
     // Developer: Alice
     // Developer: Bob
 }
}