package com.lecture_2;


	class AccessModifiersExample {
	    // Public: Accessible anywhere
	    public String publicField = "I am Public";

	    // Protected: Accessible within the same package and in subclasses
	    protected String protectedField = "I am Protected";

	    // Default (no modifier): Accessible within the same package only
	    String defaultField = "I am Default";

	    // Private: Accessible within the same class only
	    private String privateField = "I am Private";

	    // Method to access the private field
	    public void showPrivateField() {
	        System.out.println(privateField);
	    }
	}

	public class access_modifiers {
	    public static void main(String[] args) {
	        AccessModifiersExample obj = new AccessModifiersExample();

	        // Accessing public field
	        System.out.println(obj.publicField); // Accessible

	        // Accessing protected field
	        System.out.println(obj.protectedField); // Accessible (same package)

	        // Accessing default field
	        System.out.println(obj.defaultField); // Accessible (same package)

	        // Accessing private field (direct access not allowed)
	        // System.out.println(obj.privateField); // Error: privateField is not accessible

	        // Accessing private field using a public method
	        obj.showPrivateField(); // Accessible via public method
	    }
	}

/*
	Public Field:

Accessible anywhere in the code (within or outside the package).
Example: publicField.


Protected Field:

Accessible within the same package and by subclasses in other packages.
Example: protectedField.

Default Field:

Accessible only within the same package.
Example: defaultField.

Private Field:

Accessible only within the class where it is defined.
Example: privateField. Accessed via the showPrivateField method in this case.

	
	*/