package com.lecture_2;

class Shapes {
	  public void area() {
	    System.out.println("The formula for area of ");
	  }
	}
	class Triangle extends Shapes {
	  public void area() {
	    System.out.println("Triangle is ½ * base * height \n");
	  }
	}
	class Circle extends Shapes {
	  public void area() {
	    System.out.println("Circle is 3.14 * radius * radius ");
	  }
	}
	class polymorphism {
	  public static void main(String[] args) {
	    Shapes myShape = new Shapes();  // Create a Shapes object
	    Shapes myTriangle = new Triangle();  // Create a Triangle object
	    Shapes myCircle = new Circle();  // Create a Circle object
	    myShape.area();
	    myTriangle.area();
	    
	    myShape.area();
	    myCircle.area();
	  }
	}
	
	/*
	Both myTriangle and myCircle are references of type Shapes, but their runtime types are Triangle and Circle, respectively.
	At runtime, the JVM determines the appropriate area() method to call based on the object's actual type.
	Method Overriding:

	The Triangle and Circle classes override the area() method of the Shapes class.
	The overridden methods provide specific implementations for calculating the area of a triangle and a circle.
	
	*/