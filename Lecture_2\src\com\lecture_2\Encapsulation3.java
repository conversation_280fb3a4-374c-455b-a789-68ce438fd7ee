package com.lecture_2;



class Encapsulate {
    // private variables declared
    // these can only be accessed by
    // public methods of class
    private String Name;
    private int Roll;
    private int Age;

    // get method for age to access
    // private variable Age
    public int getAge() { 
    	return Age; 
    	}

    // get method for name to access
    // private variable Name
    public String getName() { 
    	return Name; 
    	}

    // get method for roll to access
    // private variable Roll
    public int getRoll() { 
    	return Roll; 
    	}

    // set method for age to access
    // private variable age
    public void setAge(int newAge) { 
    	this.Age = newAge;
    	}

    // set method for name to access
    // private variable Name
    public void setName(String newName)
    {
        this.Name = newName;
    }

    // set method for roll to access
    // private variable Roll
    public void setRoll(int newRoll) { 
    	this.Roll = newRoll; 
    	}
}

public class Encapsulation3 {
    public static void main(String[] args)
    {
        Encapsulate obj = new Encapsulate();

        // setting values of the variables
        obj.setName("Harsh");
        obj.setAge(19);
        obj.setRoll(51);

        // Displaying values of the variables
        System.out.println(" age is : " + obj.getAge());
        System.out.println("roll is : " + obj.getRoll());

        // Direct access of Roll is not possible
        // due to encapsulation
        System.out.println("name is: " +obj.getName());
    }
}

