package com.intial;

public class typecasting {
	public static void main(String[] args) {
        // Widening Casting (automatic)
        int myInt = 9;
        double myDouble = myInt; // Automatic conversion
        System.out.println("Widening Casting (int to double): " + myDouble);

        // Narrowing Casting (manual)
        double anotherDouble = 9.78;
        int anotherInt =(int) anotherDouble; // Manual conversion
        System.out.println("Narrowing Casting (double to int): " + anotherInt);
    }
}
