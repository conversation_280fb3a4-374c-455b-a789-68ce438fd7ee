package com.lecture_2;


	class BankAccount {
	    private double balance; // private field

	    // Public getter method
	    public double getBalance() {
	        return balance;
	    }

	    // Public setter method
	    public void deposit(double amount) {
	    	
	        if (amount > 0) {
	            balance += amount;
	        }
	    }
	}

	public class encapsulation1 {
	    public static void main(String[] args) {
	        BankAccount account = new BankAccount();
	        account.deposit(1000); // Set balance through a method
	        System.out.println("Balance: " + account.getBalance());
	    }
	}

