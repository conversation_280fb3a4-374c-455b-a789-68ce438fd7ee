package com.Behavioural;



//Mediator Interface
interface Mediator {
 void sendMessage(String message, Participant sender); // To send a message
}

//Concrete Mediator (Chatroom)
class SimpleChatroom implements Mediator {
 @Override
 public void sendMessage(String message, Participant sender) {
     System.out.println(sender.getName() + " says: " + message); // Displays the message with the sender's name
 }
}

//Abstract Colleague (Participant)
abstract class Participant {
 protected Mediator mediator;  // Mediator reference
 protected String name;        // Participant's name

 public Participant(Mediator mediator, String name) {
     this.mediator = mediator;
     this.name = name;
 }

 public String getName() {
     return name;
 }

 public abstract void send(String message);   // Send message via mediator
}

//Concrete Colleague (User)
class ChatParticipant extends Participant {
 public ChatParticipant(Mediator mediator, String name) {
     super(mediator, name);
 }

 @Override
 public void send(String message) {
     mediator.sendMessage(message, this); // Sends the message via the mediator
 /*
The this keyword in mediator.
sendMessage(message, this) refers to the specific ChatParticipant instance that is invoking the send method.
It is passed as the second argument to the sendMessage method of the SimpleChatroom (mediator).
This allows the mediator to know which participant (sender) sent the message, so it can use the sender's information (e.g., sender.getName()).
  */
 }
 
}

//Test Mediator Pattern
public class MediatorDP{
 public static void main(String[] args) {
     Mediator chatroom = new SimpleChatroom();  // Create a chatroom mediator

     // Create participants
     Participant user1 = new ChatParticipant(chatroom, "Alice");
     Participant user2 = new ChatParticipant(chatroom, "Bob");

     // Send messages via chatroom
     user1.send("Hello, Bob!");   // Alice sends a message
     user2.send("Hi, Alice!");    // Bob replies
 }
}



/*
 List<User>:

List: This is an interface in Java from the java.util package.
 It represents an ordered collection of elements that allows duplicate entries.
 In this case, the elements in the list are User objects.
 
 = new ArrayList<>():

new ArrayList<>(): This creates a new instance of ArrayList, which is a concrete implementation of the List interface. 
It is used to store elements dynamically.

The diamond operator <> is used to infer the type of the list 
(i.e., it automatically assumes the type User from the declaration List<User>).
 
 */
