package com.Structural;

import java.util.ArrayList;
import java.util.List;

// Observer Interface
interface Observer {
    void update(String message);
}

// Subject Class
class Subject {
    private List<Observer> observers = new ArrayList<>();

    public void addObserver(Observer observer) {
        observers.add(observer);
    }

    public void notifyObservers(String message) {
        for (Observer observer : observers) {
            observer.update(message);
        }
    }
}

// Concrete Observer
class User implements Observer {
    private String name;

    public User(String name) {
        this.name = name;
    }

    public void update(String message) {
        System.out.println(name + " received message: " + message);
    }
}

// Test Observer Pattern
public class ObserverDP {
    public static void main(String[] args) {
        Subject subject = new Subject();

        Observer user1 = new User("Alice");
        Observer user2 = new User("Bob");

        subject.addObserver(user1);
        subject.addObserver(user2);

        subject.notifyObservers("New video uploaded!");
    }
}