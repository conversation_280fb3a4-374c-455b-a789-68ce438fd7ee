package com.creational;

interface Shape {
    void draw();
}

// Concrete Products
class Circle implements Shape {
    public void draw() {
        System.out.println("Drawing a Circle");
    }
}

class Rectangle implements Shape {
    public void draw() {
        System.out.println("Drawing a Rectangle");
    }
}

// Factory Class
class ShapeFactory {
    public Shape getShape(String shapeType) {
        if (shapeType == null) return null;
        if (shapeType.equalsIgnoreCase("CIRCLE")) return new Circle();
        if (shapeType.equalsIgnoreCase("RECTANGLE")) return new Rectangle();
        return null;
    }
}

// Test Factory Method
public class FactoryDP {
    public static void main(String[] args) {
        ShapeFactory factory = new ShapeFactory();

        Shape shape1 = factory.getShape("CIRCLE");
        shape1.draw();

        Shape shape2 = factory.getShape("RECTANGLE");
        shape2.draw();
    }
}



/*
Hiring Process:
A company (factory) decides which type of employee (developer, designer, manager) to hire based on requirements. 
The client (HR manager) doesn't directly recruit employees but requests them through the hiring system.

Online Food Ordering:
You place an order (request a specific type of food). 
The system (factory) processes the request and provides the corresponding food item (product).

*/