/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/JSP_Servlet/Servlet.java to edit this template
 */

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@WebServlet("/LabReportServlet")
public class LabReportServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // Retrieve form data
        String labReferenceNumber = request.getParameter("labReferenceNumber");
        String passcode = request.getParameter("passcode");

        // Database connection variables
        String dbURL = "***************************************";
        String dbUser = "root"; // Adjust as needed for your database
        String dbPassword = ""; // Adjust as needed for your database

        // SQL query to verify the reference number and passcode
        String query = "SELECT * FROM lab_reports WHERE reference_number = ? AND passcode = ?";

        try {
            // Load the MySQL driver
            Class.forName("com.mysql.cj.jdbc.Driver");

            // Establish database connection
            Connection connection = DriverManager.getConnection(dbURL, dbUser, dbPassword);

            // Prepare the SQL statement
            PreparedStatement preparedStatement = connection.prepareStatement(query);
            preparedStatement.setString(1, labReferenceNumber);
            preparedStatement.setString(2, passcode);

            // Execute the query
            ResultSet resultSet = preparedStatement.executeQuery();

            if (resultSet.next()) {
                // Valid credentials, set attributes in the request object
                request.setAttribute("referenceNumber", resultSet.getString("reference_number"));
                request.setAttribute("testName", resultSet.getString("test_name"));
                request.setAttribute("result", resultSet.getString("result"));

                // Forward the request to the labReportDetails.jsp for rendering
                RequestDispatcher dispatcher = request.getRequestDispatcher("labReportDetails.jsp");
                dispatcher.forward(request, response);
            } else {
                // Invalid credentials, set an error attribute and forward back to the input form
                request.setAttribute("error", "Invalid Reference Number or Passcode");
                RequestDispatcher dispatcher = request.getRequestDispatcher("downloadReports.jsp");
                dispatcher.forward(request, response);
            }

            // Close resources
            resultSet.close();
            preparedStatement.close();
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "An error occurred: " + e.getMessage());
            RequestDispatcher dispatcher = request.getRequestDispatcher("downloadReports.jsp");
            dispatcher.forward(request, response);
        }
    }
}
