package com.Behavioural;


// Abstract Handler
abstract class Approver {
    protected Approver nextApprover;

    public void setNextApprover(Approver nextApprover) {
        this.nextApprover = nextApprover;
    }

    public abstract void handleRequest(int amount);
}

// Concrete Handler: Manager
class Manager extends Approver {
    public void handleRequest(int amount) {
        if (amount <= 1000) {
            System.out.println("Manager approved the request of $" + amount);
        } else if (nextApprover != null) {
            nextApprover.handleRequest(amount); // Forward to the next approver
        }
    }
}

// Concrete Handler: Director
class Director extends Approver {
    public void handleRequest(int amount) {
        if (amount <= 5000) {
            System.out.println("Director approved the request of $" + amount);
        } else if (nextApprover != null) {
            nextApprover.handleRequest(amount); // Forward to the next approver
        }
    }
}

// Concrete Handler: CEO
class CEO extends Approver {
    public void handleRequest(int amount) {
        if (amount > 5000) {
            System.out.println("CEO approved the request of $" + amount);
        }
    }
}

// Test Chain of Responsibility
public class Chain_of_responsibilityDP {
    public static void main(String[] args) {
        // Create the chain
        Approver manager = new Manager();
        Approver director = new Director();
        Approver ceo = new CEO();

        manager.setNextApprover(director); // Manager -> Director
        director.setNextApprover(ceo);     // Director -> CEO

        // Test requests
        manager.handleRequest(500);    // Manager should approve
        manager.handleRequest(3000);   // Director should approve
        manager.handleRequest(10000);  // CEO should approve
    }
}
