package com.intial;

public class Stringmethods {
    public static void main(String[] args) {
        String str = " Hello World ";
        System.out.println("Length: " + str.length());
        System.out.println("Char at index 1: " + str.charAt(1));
        System.out.println("Is Empty: " + str.isEmpty());
        System.out.println("To Upper: " + str.toUpperCase());
        System.out.println("To Lower: " + str.toLowerCase());
        System.out.println("Trimmed: " + str.trim());
        
       
        String str1 = "Java";
        String str2 = "java";

        System.out.println("Equals: " + str1.equals(str2));
        System.out.println("Equals Ignore Case: " + str1.equalsIgnoreCase(str2)); 
        /*The Unicode value of J (str1) is 74, and j (str2) is 106.
        The difference is 74 - 106 = -32.    */
        
        System.out.println("Compare To: " + str1.compareTo(str2));
        System.out.println("Contains 'av': " + str1.contains("av"));
        System.out.println("Starts With 'Ja': " + str1.startsWith("Ja"));
        System.out.println("Ends With 'va': " + str1.endsWith("va"));
    }
}