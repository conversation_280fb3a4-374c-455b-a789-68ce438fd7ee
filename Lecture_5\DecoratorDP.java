package com.Structural;

//Component Interface
interface Coffee {
 String getDescription();
 double getCost();
}

//Concrete Component
class SimpleCoffee implements Coffee {
 public String getDescription() {
     return "Simple Coffee";
 }
 public double getCost() {
     return 5.0;
 }
}

//Decorator Class
abstract class CoffeeDecorator implements Coffee {
 protected Coffee coffee;
 public CoffeeDecorator(Coffee coffee) {
     this.coffee = coffee;
 }
 public String getDescription() {
     return coffee.getDescription();
 }
 public double getCost() {
     return coffee.getCost();
 }
}

//Concrete Decorators
class MilkDecorator extends CoffeeDecorator {
 public MilkDecorator(Coffee coffee) {
     super(coffee);
 }
 public String getDescription() {
     return super.getDescription() + ", Milk";
 }
 public double getCost() {
     return super.getCost() + 2.0;
 }
}

class SugarDecorator extends CoffeeDecorator {
 public SugarDecorator(Coffee coffee) {
     super(coffee);
 }
 public String getDescription() {
     return super.getDescription() + ", Sugar";
 }
 public double getCost() {
     return super.getCost() + 1.0;
 }
}

//Test Decorator Pattern
public class DecoratorDP {
 public static void main(String[] args) {
     Coffee coffee = new SimpleCoffee();
     System.out.println(coffee.getDescription() + " -> $" + coffee.getCost());

     coffee = new MilkDecorator(coffee);
     System.out.println(coffee.getDescription() + " -> $" + coffee.getCost());

     coffee = new SugarDecorator(coffee);
     System.out.println(coffee.getDescription() + " -> $" + coffee.getCost());
 }
}