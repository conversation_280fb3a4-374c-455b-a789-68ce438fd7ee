package com.Behavioural;

interface SimpleIterator {
    boolean hasNext();
    String next();
}

// Concrete Collection
class NameCollection {
    private String[] names = {"<PERSON>", "<PERSON>", "<PERSON>"};

    // Method to get the iterator
    public SimpleIterator getIterator() {
        return new NameIterator();
    }

    // Inner Iterator Class
    private class NameIterator implements SimpleIterator {
        private int index = 0;

        // Check if more elements are available
        public boolean hasNext() {
            return index < names.length;
        }

        // Get the next element
        public String next() {
            if (hasNext()) {
            	System.out.println(index);
                return names[index++];
            }
            return null;
        }
    }
}

// Test Iterator Pattern
public class IteratorDP {
    public static void main(String[] args) {
        // Create a collection
        NameCollection nameCollection = new NameCollection();

        // Get the iterator
        SimpleIterator iterator = nameCollection.getIterator();
                                  
        // Iterate through the collection
        while (iterator.hasNext()) {
            System.out.println(iterator.next());  //next(): This method returns the next element in the collection. If there are no more elements, 
                                                   //it typically returns null.
        }
        // Output:
        // Alice
        // Bob
        // Charlie
    }
}

