package com.Structural;

interface Renderer {
    void render(String shape);
}

// Concrete Implementors
class VectorRenderer implements Renderer {
    public void render(String shape) {
        System.out.println("Rendering " + shape + " as lines.");
    }
}

class RasterRenderer implements Renderer {
    public void render(String shape) {
        System.out.println("Rendering " + shape + " as pixels.");
    }
}

// Abstraction
abstract class Shape {
    protected Renderer renderer;

    public Shape(Renderer renderer) {
        this.renderer = renderer;
    }

    abstract void draw();
}

// Refined Abstraction
class Circle extends Shape {
    public Circle(Renderer renderer) {
        super(renderer);
    }

    public void draw() {
        renderer.render("Circle");
    }
}

// Test Bridge Pattern
public class BridgeDP {
    public static void main(String[] args) {
        Shape vectorCircle = new Circle(new VectorRenderer());
        vectorCircle.draw(); // Output: Rendering Circle as lines.

        Shape rasterCircle = new Circle(new RasterRenderer());
        rasterCircle.draw(); // Output: Rendering Circle as pixels.
    }
}
