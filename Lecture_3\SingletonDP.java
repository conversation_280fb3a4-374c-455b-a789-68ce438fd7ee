package com.creational;

//Singleton Class
class Singleton {
 // Static private instance of the class
 private static Singleton singleInstance;

 // Private constructor to prevent instantiation
 private Singleton() {
     System.out.println("Singleton Instance Created");
 }

 // Public method to provide access to the instance
 public static Singleton getInstance() {
     if (singleInstance == null) 
     { // Lazy initialization
         singleInstance = new Singleton();
     }
     return singleInstance;
 }
}

//Test Singleton
public class SingletonDP {
 public static void main(String[] args) {
     Singleton instance1 = Singleton.getInstance();
     Singleton instance2 = Singleton.getInstance();

     // Verify both instances are the same
     System.out.println(instance1 == instance2);// Output: true
     //System.out.println(instance1);
 }
     
}



















/*
 In a Singleton, the idea is to ensure that only one instance of a class exists during the program's execution. 
 The static variable singleInstance in the Singleton class is responsible for holding this single instance.
 
This is the Singleton class definition. It contains a static variable singleInstance, which will hold the only instance of the class. 
The static keyword ensures that this variable is shared across all instances (which is important for Singleton). 
Initially, it's set to null since no instance is created yet.

It checks if singleInstance is null. If it is null,
it creates a new instance of the Singleton class. This process is called lazy initialization because the instance is created only when it's needed.


*/
// The variable instance1 will hold the reference to the single instance of the Singleton class


/*

Why Use the Class Name?
Since getInstance() is a static method, it is directly associated with the class (Singleton) rather than an object of the class.
Static methods can only be called using the class name, not an object.
If getInstance() were not static, you'd need an instance to call it (which defeats the purpose of the Singleton pattern).


*/