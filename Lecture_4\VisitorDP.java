package com.Behavioural;

//Element Interface
interface Visitable {
 void accept(Visitor visitor);
}

//Concrete Elements
class Book implements Visitable {
 public void accept(Visitor visitor) {
     visitor.visit(this);
 }
}

class Magazine implements Visitable {
 public void accept(Visitor visitor) {
     visitor.visit(this);
 }
}

//Visitor Interface
interface Visitor {
 void visit(Book book);
 void visit(Magazine magazine);
}

//Concrete Visitor
class DiscountVisitor implements Visitor {
 public void visit(Book book) {
     System.out.println("Applying discount to Book");
 }

 public void visit(Magazine magazine) {
     System.out.println("Applying discount to Magazine");
 }
}

//Test Visitor Pattern
public class VisitorDP {
 public static void main(String[] args) {
     Visitable book = new Book();
     Visitable magazine = new Magazine();

     Visitor discountVisitor = new DiscountVisitor();

     book.accept(discountVisitor);    // Output: Applying discount to Book
     magazine.accept(discountVisitor); // Output: Applying discount to Magazine
 }
}
